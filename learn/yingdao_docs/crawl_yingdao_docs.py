import asyncio
import os
import re
from urllib.parse import urljoin, urlparse
from crawl4ai import AsyncWebCrawler, CrawlerRunConfig

async def crawl_yingdao_docs():
    """智能爬取影刀帮助文档"""
    
    # 基础配置
    base_url = "https://www.yingdao.com"
    start_urls = [
        "https://www.yingdao.com/yddoc/rpa/help",
        "https://www.yingdao.com/yddoc/rpa",
    ]
    
    output_dir = "yingdao_docs"
    os.makedirs(output_dir, exist_ok=True)
    
    # 关键词过滤 - 只爬取包含这些关键词的页面
    include_keywords = [
        "功能文档", "接口文档", "API", "指令文档", "开放API", 
        "管理文档", "快速入门", "概述", "常见问题", "专题文档",
        "解决方案", "帮助文档", "教程", "使用说明"
    ]
    
    # 排除关键词 - 避免爬取这些页面
    exclude_keywords = [
        "登录", "注册", "购买", "价格", "联系", "关于我们",
        "隐私政策", "服务条款", "招聘", "新闻", "博客"
    ]
    
    visited_urls = set()
    urls_to_crawl = set(start_urls)
    
    config = CrawlerRunConfig(
        word_count_threshold=50,
        cache_mode="bypass",
        wait_for_images=False,
        page_timeout=30000,
        delay_before_return_html=2000,
        exclude_external_links=True
    )
    
    def is_relevant_url(url, title="", text=""):
        """判断URL是否相关"""
        url_lower = url.lower()
        title_lower = title.lower()
        text_lower = text.lower()
        
        # 必须包含yddoc路径
        if "/yddoc/" not in url_lower:
            return False
            
        # 排除明显无关的URL
        for keyword in exclude_keywords:
            if keyword in url_lower or keyword in title_lower:
                return False
        
        # 包含相关关键词
        content = f"{url_lower} {title_lower} {text_lower}"
        for keyword in include_keywords:
            if keyword in content:
                return True
                
        # 如果是yddoc下的页面，默认认为相关
        if "/yddoc/rpa" in url_lower:
            return True
            
        return False
    
    def extract_links(result):
        """提取页面中的相关链接"""
        links = []
        if result.links:
            for link in result.links:
                href = link.get('href', '')
                text = link.get('text', '')
                
                if href:
                    # 转换为绝对URL
                    absolute_url = urljoin(base_url, href)
                    
                    # 检查是否相关
                    if is_relevant_url(absolute_url, text, text):
                        links.append({
                            'url': absolute_url,
                            'text': text,
                            'href': href
                        })
        return links
    
    async with AsyncWebCrawler() as crawler:
        crawl_count = 0
        max_crawls = 100  # 限制最大爬取数量
        
        while urls_to_crawl and crawl_count < max_crawls:
            current_url = urls_to_crawl.pop()
            
            if current_url in visited_urls:
                continue
                
            visited_urls.add(current_url)
            crawl_count += 1
            
            print(f"\n[{crawl_count}/{max_crawls}] 爬取: {current_url}")
            
            try:
                result = await crawler.arun(url=current_url, config=config)
                
                if result.success and result.markdown.strip():
                    content_length = len(result.markdown.strip())
                    print(f"✅ 成功 - 内容长度: {content_length} 字符")
                    
                    # 生成文件名
                    parsed_url = urlparse(current_url)
                    safe_filename = (parsed_url.path + parsed_url.fragment).replace("/", "_").replace("#", "_")
                    if not safe_filename or safe_filename == "_":
                        safe_filename = "index"
                    safe_filename = re.sub(r'[^\w\-_.]', '_', safe_filename) + ".md"
                    
                    # 保存文件
                    output_path = os.path.join(output_dir, safe_filename)
                    with open(output_path, 'w', encoding='utf-8') as f:
                        f.write(f"# {current_url}\n\n")
                        f.write(result.markdown)
                    
                    print(f"📁 保存到: {output_path}")
                    
                    # 提取新链接
                    new_links = extract_links(result)
                    new_count = 0
                    for link in new_links:
                        if link['url'] not in visited_urls:
                            urls_to_crawl.add(link['url'])
                            new_count += 1
                    
                    if new_count > 0:
                        print(f"🔗 发现 {new_count} 个新链接")
                        
                else:
                    print(f"❌ 失败或内容为空: {result.error if not result.success else '内容为空'}")
                    
            except Exception as e:
                print(f"⚠️ 异常: {str(e)}")
            
            # 避免请求过快
            await asyncio.sleep(1)
    
    print(f"\n🎉 爬取完成！")
    print(f"📊 总共爬取了 {crawl_count} 个页面")
    print(f"📁 文件保存在: {output_dir}/")

if __name__ == "__main__":
    asyncio.run(crawl_yingdao_docs())
